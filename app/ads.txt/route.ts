import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Extract domain from NEXT_PUBLIC_SITE_URL
    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL;
    if (!siteUrl) {
      return new NextResponse('NEXT_PUBLIC_SITE_URL environment variable is not set', {
        status: 500,
        headers: { 'Content-Type': 'text/plain' },
      });
    }

    // Remove protocol and get domain
    const domain = siteUrl.replace(/^https?:\/\//, '');
    
    // Fetch ads.txt content from Ezoic's ads.txt manager
    const ezoicUrl = `https://srv.adstxtmanager.com/19390/${domain}`;
    const response = await fetch(ezoicUrl);
    
    if (!response.ok) {
      return new NextResponse(`Failed to fetch ads.txt from Ezoic: ${response.status}`, {
        status: response.status,
        headers: { 'Content-Type': 'text/plain' },
      });
    }
    
    const content = await response.text();
    
    return new NextResponse(content, {
      headers: {
        'Content-Type': 'text/plain',
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      },
    });
  } catch (error) {
    console.error('Error fetching ads.txt:', error);
    return new NextResponse('Internal Server Error', {
      status: 500,
      headers: { 'Content-Type': 'text/plain' },
    });
  }
}
