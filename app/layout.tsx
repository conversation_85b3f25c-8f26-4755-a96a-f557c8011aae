import { ColorSchemeScript, mantineHtmlProps } from '@mantine/core';
import { MantineProviders } from '../src/components/MantineProviders';
import { GoogleAnalytics } from '../src/components/GoogleAnalytics';
import { GoogleAds } from '../src/components/GoogleAds';
import { EzoicAds } from '../src/components/EzoicAds';
import '@mantine/core/styles.css';
import '../src/index.css';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Free QR Code Generator Online | QRCode Donkey',
  description:
    'Generate custom QR codes for free with QRCode Donkey. Create high-quality QR codes for websites, social media, business cards, and more. No sign-up required!',
  icons: {
    icon: '/donkey-128.png',
  },
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" {...mantineHtmlProps}>
      <head>
        <ColorSchemeScript />
        <EzoicAds verificationId={process.env.NEXT_PUBLIC_EZOIC_VERIFICATION_ID} />
      </head>
      <body>
        <GoogleAnalytics measurementId={process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID!} />
        <GoogleAds publisherId={process.env.NEXT_PUBLIC_GOOGLE_ADS_CLIENT_ID!} />
        <MantineProviders>{children}</MantineProviders>
      </body>
    </html>
  );
}
