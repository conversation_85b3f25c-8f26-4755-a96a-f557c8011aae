# Ezoic Integration Guide

This document explains how to use the Ezoic ads integration in your Next.js application.

## Overview

The Ezoic integration follows the official [Ezoic JavaScript integration guide](https://docs.ezoic.com/docs/ezoicads/integration/) and includes:

1. **Header Scripts Integration** - Privacy scripts and main Ezoic script
2. **Ad Placement Components** - React components for displaying ads
3. **Environment Configuration** - Easy enable/disable functionality

## Setup Steps

### 1. Environment Variables

Add the following to your `.env.local` file:

```env
# Enable Ezoic ads (set to "true" to enable)
NEXT_PUBLIC_EZOIC_ENABLED=true
```

### 2. Header Scripts (Already Integrated)

The `EzoicAds` component is already integrated in `app/layout.tsx` and includes:

- **Privacy Scripts**: GDPR compliance scripts that load first
- **Main Ezoic Script**: The core Ezoic advertising script
- **Standalone Configuration**: Sets up the `ezstandalone` global object

### 3. Ads.txt Setup

You need to set up an ads.txt file for your domain. Choose one of these methods:

#### Option A: Server Redirect (Recommended)

Add this to your server configuration:

**Apache (.htaccess):**
```apache
Redirect 301 /ads.txt https://srv.adstxtmanager.com/19390/[YOUR_DOMAIN].com
```

**Nginx:**
```nginx
server {
    location ~ /ads.txt {
        return 301 https://srv.adstxtmanager.com/19390/[YOUR_DOMAIN].com;
    }
}
```

#### Option B: Next.js API Route

Create `app/ads.txt/route.ts`:

```typescript
import { NextResponse } from 'next/server';

export async function GET() {
  const response = await fetch(`https://srv.adstxtmanager.com/19390/${process.env.NEXT_PUBLIC_SITE_URL?.replace('https://', '')}`);
  const content = await response.text();
  
  return new NextResponse(content, {
    headers: {
      'Content-Type': 'text/plain',
    },
  });
}
```

### 4. Create Ad Placements

1. Log into your [Ezoic Dashboard](https://pubdash.ezoic.com/ezoicads/adpositions/placeholders)
2. Create ad placements and note the placement IDs
3. Use the placement components in your React components

## Usage

### Single Ad Placement

```tsx
import { EzoicAdPlacement } from '../src/components/EzoicAdPlacement';

export function MyComponent() {
  return (
    <div>
      <h1>My Content</h1>
      <EzoicAdPlacement placementId={101} />
      <p>More content...</p>
    </div>
  );
}
```

### Multiple Ad Placements (Optimized)

For pages with multiple ads, use `EzoicMultiAdPlacement` for better performance:

```tsx
import { EzoicMultiAdPlacement } from '../src/components/EzoicAdPlacement';

export function MyPage() {
  return (
    <div>
      <h1>My Page</h1>
      <EzoicMultiAdPlacement placementIds={[101, 102, 103]} />
    </div>
  );
}
```

### Show All Ads on Page

```tsx
import { showAllEzoicAds } from '../src/components/EzoicAdPlacement';
import { useEffect } from 'react';

export function MyPage() {
  useEffect(() => {
    // Show all Ezoic ads on this page
    showAllEzoicAds();
  }, []);

  return (
    <div>
      <div id="ezoic-pub-ad-placeholder-101" />
      <div id="ezoic-pub-ad-placeholder-102" />
    </div>
  );
}
```

## Components

### EzoicAds

Located in `src/components/EzoicAds.tsx`:
- Loads privacy scripts for GDPR compliance
- Loads main Ezoic header script
- Initializes ezstandalone configuration
- Controlled by `NEXT_PUBLIC_EZOIC_ENABLED` environment variable

### EzoicAdPlacement

Located in `src/components/EzoicAdPlacement.tsx`:
- `EzoicAdPlacement` - Single ad placement
- `EzoicMultiAdPlacement` - Multiple ad placements (optimized)
- `showAllEzoicAds()` - Utility function to show all ads on a page

## Best Practices

1. **Don't style placeholder divs** - Ezoic handles all styling
2. **Use multi-placement for multiple ads** - Reduces server requests
3. **Test with ads.txt** - Verify `yourdomain.com/ads.txt` works
4. **Enable gradually** - Start with a few placements and optimize

## Troubleshooting

### Ads Not Showing

1. Check that `NEXT_PUBLIC_EZOIC_ENABLED=true` in your environment
2. Verify ads.txt is accessible at `yourdomain.com/ads.txt`
3. Ensure placement IDs match your Ezoic dashboard
4. Check browser console for JavaScript errors

### GDPR Compliance

The privacy scripts are automatically loaded and handle GDPR compliance. No additional configuration is needed.

## Environment Variables

```env
# Required for Ezoic
NEXT_PUBLIC_EZOIC_ENABLED=true

# Required for ads.txt (if using API route method)
NEXT_PUBLIC_SITE_URL=https://your-domain.com
```
