# Google Ads, Analytics & Ezoic Setup Guide

This document explains how to use the Google Ads, Analytics, and Ezoic implementation in your Next.js application.

## Environment Variables

Make sure to set these environment variables in your `.env.local` file:

```env
# Google Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-RCMH6J3KQ9

# Google Ads
NEXT_PUBLIC_GOOGLE_ADS_CLIENT_ID=ca-pub-7017601566406856

# Ezoic Ads (set to "true" to enable)
NEXT_PUBLIC_EZOIC_ENABLED=true

# Site URL (used for sitemaps and robots.txt)
NEXT_PUBLIC_SITE_URL=https://your-domain.com
```

## Components

### GoogleAnalytics Component

Located in `src/components/GoogleAnalytics.tsx`, this component handles:
- Loading Google Analytics scripts
- Initializing gtag
- Providing helper functions for tracking

### GoogleAds Component

Located in `src/components/GoogleAds.tsx`, this component handles:
- Loading Google AdSense scripts
- Setting up ad meta tags
- Providing ad banner components

### AdUnit Component

Located in `src/components/AdUnit.tsx`, provides reusable ad components:
- `AdUnit` - Generic ad unit
- `BannerAd` - Horizontal banner ads
- `SquareAd` - Square/rectangle ads
- `SidebarAd` - Vertical sidebar ads

### EzoicAds Component

Located in `src/components/EzoicAds.tsx`, this component handles:
- Loading Ezoic privacy scripts (GDPR compliance)
- Loading the main Ezoic header script
- Initializing the Ezoic standalone configuration
- Can be enabled/disabled via the `NEXT_PUBLIC_EZOIC_ENABLED` environment variable

The component follows Ezoic's integration guidelines by:
1. Loading privacy scripts first for GDPR compliance
2. Loading the main Ezoic script with proper async attributes
3. Setting up the `ezstandalone` global object for ad placements

For detailed Ezoic setup instructions, see [EZOIC_INTEGRATION.md](./EZOIC_INTEGRATION.md).

## Analytics Hook

Use the `useAnalytics` hook in your components to track events:

```tsx
import { useAnalytics } from '../hooks/useAnalytics';

function MyComponent() {
  const { trackEvent, trackQRCodeGeneration, trackEngagement } = useAnalytics();

  const handleClick = () => {
    trackEvent({
      action: 'button_click',
      category: 'User Interaction',
      label: 'Header CTA',
    });
  };

  return <button onClick={handleClick}>Click me</button>;
}
```

## Available Tracking Functions

### trackEvent
```tsx
trackEvent({
  action: 'custom_action',
  category: 'Category',
  label: 'Optional label',
  value: 123 // Optional numeric value
});
```

### trackQRCodeGeneration
```tsx
trackQRCodeGeneration(url, dotType);
```

### trackQRCodeDownload
```tsx
trackQRCodeDownload('png'); // or 'svg', 'jpg', etc.
```

### trackConversion
```tsx
trackConversion('conversion_label', {
  currency: 'USD',
  value: 10.00,
  transaction_id: 'txn_123'
});
```

### trackEngagement
```tsx
trackEngagement('page_scroll', 'bottom_reached');
```

## Adding Ad Units

### Basic Banner Ad
```tsx
import { BannerAd } from '../components/AdUnit';

<BannerAd adSlot="your-ad-slot-id" />
```

### Custom Ad Unit
```tsx
import { AdUnit } from '../components/AdUnit';

<AdUnit
  adSlot="your-ad-slot-id"
  adFormat="rectangle"
  width={300}
  height={250}
/>
```

## Getting Ad Slot IDs

1. Go to your Google AdSense account
2. Navigate to "Ads" > "By ad unit"
3. Create a new ad unit or use existing ones
4. Copy the ad slot ID (data-ad-slot value)
5. Replace the placeholder "**********" in your components

## Performance Considerations

- Scripts are loaded with `strategy="afterInteractive"` for optimal performance
- Ad units are lazy-loaded and only initialize when needed
- Analytics events are batched and sent efficiently

## Privacy & GDPR Compliance

Consider implementing:
- Cookie consent banners
- Analytics opt-out functionality
- GDPR-compliant data handling

## Testing

- Use Google Analytics Real-Time reports to verify tracking
- Use Google AdSense reports to monitor ad performance
- Test in development with actual IDs (ads won't show revenue in dev)

## Troubleshooting

### Analytics not tracking
- Check browser console for errors
- Verify environment variables are set
- Ensure gtag is loaded (check Network tab)

### Ads not showing
- Verify AdSense account is approved
- Check ad slot IDs are correct
- Ensure site is added to AdSense
- Check for ad blockers during testing
