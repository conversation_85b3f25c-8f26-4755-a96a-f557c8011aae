'use client';

import { Stack, Title, Text, Card } from '@mantine/core';
import { EzoicAdPlacement, EzoicMultiAdPlacement } from './EzoicAdPlacement';

/**
 * Example component showing how to integrate Ezoic ads into your pages
 * 
 * This is a demonstration component - replace the placement IDs with your actual
 * placement IDs from the Ezoic dashboard.
 */
export function ExampleEzoicIntegration() {
  return (
    <Stack gap="xl">
      <Title order={2}>Example: Ezoic Ad Integration</Title>
      
      <Card withBorder padding="md">
        <Text size="sm" c="dimmed" mb="md">
          This shows how to integrate Ezoic ads into your content. 
          Replace the placeholder IDs (101, 102, etc.) with your actual placement IDs.
        </Text>
        
        {/* Single ad placement - good for header/footer ads */}
        <Title order={4} mb="sm">Single Ad Placement (Header)</Title>
        <EzoicAdPlacement 
          placementId={101} 
          className="mb-4"
        />
        
        <Text mb="lg">
          Your main content goes here. This could be articles, product descriptions, 
          or any other content on your page.
        </Text>
        
        {/* Multiple ad placements - optimized for pages with several ads */}
        <Title order={4} mb="sm">Multiple Ad Placements (Optimized)</Title>
        <Text size="sm" c="dimmed" mb="sm">
          For pages with multiple ads, use EzoicMultiAdPlacement for better performance:
        </Text>
        
        <EzoicMultiAdPlacement 
          placementIds={[102, 103, 104]}
          className="my-4"
        />
        
        <Text mt="lg">
          More content can go here. The ads will be automatically loaded and displayed
          by Ezoic's system based on your placement configuration.
        </Text>
        
        {/* Another single placement for sidebar or footer */}
        <Title order={4} mb="sm" mt="lg">Sidebar/Footer Ad</Title>
        <EzoicAdPlacement 
          placementId={105}
          className="mt-4"
        />
      </Card>
      
      <Card withBorder padding="md">
        <Title order={4} mb="sm">Integration Notes</Title>
        <Stack gap="xs">
          <Text size="sm">
            • Replace placeholder IDs (101-105) with your actual Ezoic placement IDs
          </Text>
          <Text size="sm">
            • Create placements in your Ezoic dashboard first
          </Text>
          <Text size="sm">
            • Don't add custom styling to the ad containers
          </Text>
          <Text size="sm">
            • Use EzoicMultiAdPlacement for pages with multiple ads
          </Text>
          <Text size="sm">
            • Ensure NEXT_PUBLIC_EZOIC_ENABLED=true in your environment
          </Text>
        </Stack>
      </Card>
    </Stack>
  );
}
