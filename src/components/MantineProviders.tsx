'use client';

import { MantineProvider, createTheme, Container } from '@mantine/core';
import { AppHeader } from '../AppHeader';

const theme = createTheme({
  primaryColor: 'cyan',
  colors: {
    dark: [
      '#C1C2C5',
      '#A6A7AB',
      '#909296',
      '#5C5F66',
      '#373A40',
      '#2C2E33',
      '#25262B',
      '#1A1B1E',
      '#141517',
      '#101113',
    ],
  },
  radius: {
    sm: '12px',
    md: '16px',
    lg: '20px',
    xl: '24px',
  },
});

export function MantineProviders({ children }: { children: React.ReactNode }) {
  return (
    <MantineProvider theme={theme} defaultColorScheme="auto">
      <AppHeader />
      <Container fluid maw={1200} pt="xl" pb="xl">
        {children}
      </Container>
    </MantineProvider>
  );
}