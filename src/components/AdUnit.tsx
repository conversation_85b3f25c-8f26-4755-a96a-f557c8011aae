'use client';

import { useEffect, useRef } from 'react';
import { Box } from '@mantine/core';

interface AdUnitProps {
  adSlot: string;
  adFormat?: 'auto' | 'rectangle' | 'vertical' | 'horizontal' | 'fluid';
  fullWidthResponsive?: boolean;
  style?: React.CSSProperties;
  className?: string;
  width?: number;
  height?: number;
}

export function AdUnit({
  adSlot,
  adFormat = 'auto',
  fullWidthResponsive = true,
  style,
  className,
  width,
  height,
}: AdUnitProps) {
  const adRef = useRef<HTMLModElement>(null);

  useEffect(() => {
    if (typeof window !== 'undefined' && window.adsbygoogle && adRef.current) {
      try {
        // Check if the ad has already been pushed
        if (!adRef.current.hasAttribute('data-adsbygoogle-status')) {
          (window.adsbygoogle = window.adsbygoogle || []).push({});
        }
      } catch (err) {
        console.error('AdSense error:', err);
      }
    }
  }, []);

  const adStyle: React.CSSProperties = {
    display: 'block',
    textAlign: 'center',
    ...style,
  };

  if (width && height) {
    adStyle.width = width;
    adStyle.height = height;
  }

  return (
    <Box className={className} style={{ textAlign: 'center', margin: '20px 0' }}>
      <ins
        ref={adRef}
        className="adsbygoogle"
        style={adStyle}
        data-ad-client={process.env.NEXT_PUBLIC_GOOGLE_ADS_CLIENT_ID}
        data-ad-slot={adSlot}
        data-ad-format={adFormat}
        data-full-width-responsive={fullWidthResponsive.toString()}
      />
    </Box>
  );
}

// Predefined ad components for common use cases
export function BannerAd({ adSlot, className }: { adSlot: string; className?: string }) {
  return (
    <AdUnit
      adSlot={adSlot}
      adFormat="horizontal"
      className={className}
      style={{ minHeight: '90px' }}
    />
  );
}

export function SquareAd({ adSlot, className }: { adSlot: string; className?: string }) {
  return (
    <AdUnit
      adSlot={adSlot}
      adFormat="rectangle"
      width={300}
      height={250}
      className={className}
    />
  );
}

export function SidebarAd({ adSlot, className }: { adSlot: string; className?: string }) {
  return (
    <AdUnit
      adSlot={adSlot}
      adFormat="vertical"
      width={160}
      height={600}
      className={className}
    />
  );
}
