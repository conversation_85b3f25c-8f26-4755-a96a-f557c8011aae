'use client';

import { useRef, useEffect, useCallback, MutableRefObject } from 'react';
import QRCodeStyling, { Options } from 'qr-code-styling';
import { DotType } from 'qr-code-styling';
import style from './QRCodeDisplay.module.css';

interface QRCodeDisplayProps {
  data: string;
  dotType: DotType;
  dotColor: string;
  backgroundColor: string;
  size?: number;
  image?: string;
  qrCodeRef?: MutableRefObject<QRCodeStyling | null>;
}

export function QRCodeDisplay({
  data,
  dotType,
  dotColor,
  backgroundColor,
  size = 300,
  image,
  qrCodeRef,
}: QRCodeDisplayProps) {
  const ref = useRef<HTMLDivElement>(null);
  const qrCodeInstance = useRef<QRCodeStyling | null>(null);

  const getQrCodeOptions = useCallback(() => {
    return {
      width: 600,
      height: 600,
      margin: 15,
      type: 'canvas',
      data: data,
      image: image,
      dotsOptions: {
        color: dotColor,
        type: dotType,
      },
      backgroundOptions: {
        color: backgroundColor,
      },
      imageOptions: {
        imageSize: 0.2,
        margin: 4,
        crossOrigin: 'anonymous',
        hideBackgroundDots: true,
        saveAsBlob: true,
      },
    } satisfies Partial<Options>;
  }, [data, dotColor, dotType, backgroundColor, size]);

  useEffect(() => {
    if (ref.current) {
      // Initialize QRCodeStyling instance on client side only
      if (!qrCodeInstance.current) {
        qrCodeInstance.current = new QRCodeStyling(getQrCodeOptions());
        qrCodeInstance.current.append(ref.current);
      } else {
        qrCodeInstance.current.update(getQrCodeOptions());
      }
      if (qrCodeRef) {
        qrCodeRef.current = qrCodeInstance.current;
      }
    }
  }, [getQrCodeOptions, qrCodeRef]);

  return (
    <div
      className={style.qrcode}
      ref={ref}
      style={{
        border: `1px solid ${dotColor}`,
        height: size,
        width: size,
      }}
    />
  );
}
