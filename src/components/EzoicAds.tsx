'use client';

import Script from 'next/script';

interface EzoicAdsProps {
  verificationId?: string;
}

export function EzoicAds({ verificationId }: EzoicAdsProps) {
  if (!verificationId) {
    return null;
  }

  return (
    <>
      <meta name="ezoic-site-verification" content={verificationId} />
      {/* Privacy Scripts - Must be loaded first */}
      <Script
        src="https://cmp.gatekeeperconsent.com/min.js"
        strategy="beforeInteractive"
        data-cfasync="false"
      />
      <Script
        src="https://the.gatekeeperconsent.com/cmp.min.js"
        strategy="beforeInteractive"
        data-cfasync="false"
      />

      {/* Main Ezoic Header Script */}
      <Script src="//www.ezojs.com/ezoic/sa.min.js" strategy="beforeInteractive" async />

      {/* Ezoic Standalone Configuration */}
      <Script
        id="ezoic-standalone-config"
        strategy="beforeInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.ezstandalone = window.ezstandalone || {};
            ezstandalone.cmd = ezstandalone.cmd || [];
          `,
        }}
      />
    </>
  );
}

// Extend the Window interface to include Ezoic globals
declare global {
  interface Window {
    ezstandalone: {
      cmd: any[];
      [key: string]: any;
    };
  }
}
