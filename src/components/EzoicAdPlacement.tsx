'use client';

import { useEffect } from 'react';

interface EzoicAdPlacementProps {
  placementId: number;
  className?: string;
}

export function EzoicAdPlacement({ placementId, className }: EzoicAdPlacementProps) {
  useEffect(() => {
    // Only run on client side and if <PERSON><PERSON> is available
    if (typeof window !== 'undefined' && window.ezstandalone) {
      // Add the showAds command to the queue
      window.ezstandalone.cmd.push(() => {
        window.ezstandalone.showAds(placementId);
      });
    }
  }, [placementId]);

  return (
    <div 
      id={`ezoic-pub-ad-placeholder-${placementId}`}
      className={className}
    />
  );
}

interface EzoicMultiAdPlacementProps {
  placementIds: number[];
  className?: string;
}

export function EzoicMultiAdPlacement({ placementIds, className }: EzoicMultiAdPlacementProps) {
  useEffect(() => {
    // Only run on client side and if <PERSON><PERSON> is available
    if (typeof window !== 'undefined' && window.ezstandalone && placementIds.length > 0) {
      // Add the showAds command to the queue with multiple placement IDs
      window.ezstandalone.cmd.push(() => {
        window.ezstandalone.showAds(...placementIds);
      });
    }
  }, [placementIds]);

  return (
    <div className={className}>
      {placementIds.map((placementId) => (
        <div 
          key={placementId}
          id={`ezoic-pub-ad-placeholder-${placementId}`}
        />
      ))}
    </div>
  );
}

// Utility function to show ads for all placements on a page
export function showAllEzoicAds() {
  if (typeof window !== 'undefined' && window.ezstandalone) {
    window.ezstandalone.cmd.push(() => {
      // Calling showAds() without parameters shows all placements on the page
      window.ezstandalone.showAds();
    });
  }
}
