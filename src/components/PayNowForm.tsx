'use client';

import { useState } from 'react';
import {
  Button,
  Stack,
  TextInput,
  NumberInput,
  Radio,
  Switch,
  Group,
  Card,
  Title,
  Alert,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { IconInfoCircle } from '@tabler/icons-react';
import { PayNowOptions, isValidUEN, isValidSingaporeMobile } from '../utils/paynow';

interface PayNowFormProps {
  onGenerate: (options: PayNowOptions) => void;
}

export function PayNowForm({ onGenerate }: PayNowFormProps) {
  const [showOptionalFields, setShowOptionalFields] = useState(false);

  const form = useForm({
    mode: 'uncontrolled',
    initialValues: {
      identifierType: 'mobile' as 'mobile' | 'uen',
      identifier: '',
      amount: 0,
      editable: true,
      expiry: '',
      refNumber: '',
      merchantName: '',
      merchantCity: 'Singapore',
    },
    validate: {
      identifier: (value, values) => {
        if (!value) return 'Identifier is required';

        if (values.identifierType === 'mobile') {
          if (!isValidSingaporeMobile(value)) {
            return 'Please enter a valid Singapore mobile number (e.g., +65********, ********)';
          }
        } else {
          if (!isValidUEN(value)) {
            return 'Please enter a valid UEN (e.g., 200012345Z)';
          }
        }
        return null;
      },
      amount: (value) => {
        // if can edit amount, no need to validate
        if (form.getValues().editable) return null;
        if (!value || value <= 0) return 'Amount must be greater than 0';
        if (value > 999999.99) return 'Amount cannot exceed $999,999.99';
        return null;
      },
      expiry: (value) => {
        if (value && !/^\d{8}(\d{6})?$/.test(value)) {
          return 'Expiry must be in YYYYMMDD or YYYYMMDDHHmmss format';
        }
        return null;
      },
      refNumber: (value) => {
        if (value && value.length > 25) {
          return 'Reference number cannot exceed 25 characters';
        }
        return null;
      },
      merchantName: (value) => {
        if (value && value.length > 25) {
          return 'Merchant name cannot exceed 25 characters';
        }
        return null;
      },
    },
  });

  const handleSubmit = (values: typeof form.values) => {
    const options: PayNowOptions = {
      identifier: values.identifier,
      identifierType: values.identifierType,
      amount: values.amount,
      editable: values.editable ? 1 : 0,
      expiry: values.expiry || undefined,
      refNumber: values.refNumber || undefined,
      merchantName: values.merchantName || undefined,
      merchantCity: values.merchantCity || 'Singapore',
    };

    onGenerate(options);
  };

  const getIdentifierPlaceholder = () => {
    return form.getValues().identifierType === 'mobile' ? '+65******** or ********' : '200012345Z';
  };

  const getIdentifierLabel = () => {
    return form.getValues().identifierType === 'mobile'
      ? 'Mobile Number'
      : 'UEN (Unique Entity Number)';
  };

  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder>
      <Alert mb="md" color="blue">
        Generate QR codes for Singapore PayNow payments. Recipients can scan with any Singapore bank
        app.
      </Alert>

      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Stack gap="md">
          <Radio.Group
            label="Identifier Type"
            description="Choose whether you're receiving payments as an individual or business"
            key={form.key('identifierType')}
            {...form.getInputProps('identifierType')}
            required
          >
            <Group mt="xs">
              <Radio value="mobile" label="Mobile Number (Individual)" />
              <Radio value="uen" label="UEN (Business/Company)" />
            </Group>
          </Radio.Group>

          <TextInput
            label={getIdentifierLabel()}
            placeholder={getIdentifierPlaceholder()}
            description={
              form.getValues().identifierType === 'mobile'
                ? 'Your registered PayNow mobile number'
                : "Your company's Unique Entity Number"
            }
            key={form.key('identifier')}
            {...form.getInputProps('identifier')}
            required
          />

          <NumberInput
            label="Amount (SGD)"
            placeholder="0.00"
            description="Payment amount in Singapore Dollars"
            min={0.0}
            max={999999.99}
            decimalScale={2}
            fixedDecimalScale
            key={form.key('amount')}
            {...form.getInputProps('amount')}
            required
          />

          <Group>
            <Switch
              label="Allow amount editing"
              description="Let payers modify the amount when scanning"
              key={form.key('editable')}
              {...form.getInputProps('editable', { type: 'checkbox' })}
            />
          </Group>

          <Group>
            <Switch
              label="Show optional fields"
              description="Toggle to show/hide optional fields like expiry, reference number, merchant name, and city."
              checked={showOptionalFields}
              onChange={(event) => setShowOptionalFields(event.currentTarget.checked)}
            />
          </Group>

          {showOptionalFields && (
            <>
              <TextInput
                label="Expiry Date (Optional)"
                placeholder="20241231 or 20241231235959"
                description="YYYYMMDD or YYYYMMDDHHmmss format. Leave empty for no expiry."
                key={form.key('expiry')}
                {...form.getInputProps('expiry')}
              />

              <TextInput
                label="Reference Number (Optional)"
                placeholder="INV-001"
                description="For tracking purposes (max 25 characters)"
                key={form.key('refNumber')}
                {...form.getInputProps('refNumber')}
              />

              <TextInput
                label="Merchant Name (Optional)"
                placeholder="Your Business Name"
                description="Name displayed to payers (max 25 characters)"
                key={form.key('merchantName')}
                {...form.getInputProps('merchantName')}
              />

              <TextInput
                label="City"
                description="City where the business is located"
                key={form.key('merchantCity')}
                {...form.getInputProps('merchantCity')}
              />
            </>
          )}

          <Button type="submit" size="md" fullWidth>
            Generate PayNow QR Code
          </Button>
        </Stack>
      </form>
    </Card>
  );
}
