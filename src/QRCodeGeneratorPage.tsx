'use client';
import { useEffect, useState, useRef } from 'react';
import { Stack, Flex, Grid, Button, Center, Card, Title } from '@mantine/core';
import type { DotType, FileExtension } from 'qr-code-styling';
import QRCodeStyling from 'qr-code-styling';
import { QRCodeForm } from './components/QRCodeForm';
import { QRCodeDisplay } from './components/QRCodeDisplay';
import { QRCodeHistory } from './components/QRCodeHistory';
import { useAnalytics } from './hooks/useAnalytics';
import { trackEvent } from './components/GoogleAnalytics';
import { EzoicAdPlacement } from './components/EzoicAdPlacement';

interface HistoryEntry {
  url: string;
  dotType: DotType;
  dotColor: string;
  backgroundColor: string;
  timestamp: number;
}

const LOCAL_STORAGE_KEY = 'qr_code_history';
const MAX_HISTORY_ENTRIES = 100;

const DEFAULT_URL = '';
const DEFAULT_DOT_TYPE: DotType = 'square';
const DEFAULT_DOT_COLOR = '#000000';
const DEFAULT_BACKGROUND_COLOR = '#ffffff';

const loadHistory = () => {
  try {
    const storedHistory = localStorage.getItem(LOCAL_STORAGE_KEY);
    return storedHistory ? JSON.parse(storedHistory) : [];
  } catch (error) {
    console.error('Failed to load history from local storage:', error);
    return [];
  }
};

const getInitialParams = () => {
  if (typeof window === 'undefined') return {};
  const urlParams = new URLSearchParams(window.location.search);
  const configParam = urlParams.get('config');
  if (configParam) {
    try {
      const decodedConfig = JSON.parse(atob(configParam));
      return decodedConfig;
    } catch (error) {
      console.error('Failed to parse config from URL:', error);
    }
  }
  return {};
};

export default function QRCodeGeneratorPage() {
  const {
    url: initialUrl = DEFAULT_URL,
    dotType: initialDotType = DEFAULT_DOT_TYPE,
    dotColor: initialDotColor = DEFAULT_DOT_COLOR,
    backgroundColor: initialBackgroundColor = DEFAULT_BACKGROUND_COLOR,
  } = getInitialParams();
  const [url, setUrl] = useState(initialUrl);
  const [dotType, setDotType] = useState<DotType>(initialDotType);
  const [dotColor, setDotColor] = useState(initialDotColor);
  const [backgroundColor, setBackgroundColor] = useState(initialBackgroundColor);
  const [history, setHistory] = useState<HistoryEntry[]>([]);
  const qrCodeRef = useRef<QRCodeStyling | null>(null);

  const { trackQRCodeGeneration, trackEngagement, trackQRCodeDownload } = useAnalytics();

  const saveHistory = (newHistory: HistoryEntry[]) => {
    try {
      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(newHistory));
    } catch (error) {
      console.error('Failed to save history to local storage:', error);
    }
  };

  useEffect(() => {
    setHistory(loadHistory());
  }, []);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const config = { url, dotType, dotColor, backgroundColor };

    const isDefaultConfig =
      url === DEFAULT_URL &&
      dotType === DEFAULT_DOT_TYPE &&
      dotColor === DEFAULT_DOT_COLOR &&
      backgroundColor === DEFAULT_BACKGROUND_COLOR;

    if (isDefaultConfig) {
      urlParams.delete('config');
    } else {
      const encodedConfig = btoa(JSON.stringify(config));
      urlParams.set('config', encodedConfig);
    }

    // These parameters are now handled by the 'config' parameter, so they should always be deleted.
    urlParams.delete('dotType');
    urlParams.delete('dotColor');
    urlParams.delete('backgroundColor');

    window.history.replaceState(
      {},
      '',
      `${window.location.pathname}${isDefaultConfig ? '' : '?'}${urlParams}`
    );
  }, [url, dotType, dotColor, backgroundColor]);

  const handleGenerateQRCode = async (values: {
    url: string;
    dotType: DotType;
    dotColor: string;
    backgroundColor: string;
  }) => {
    setUrl(values.url);
    setDotType(values.dotType);
    setDotColor(values.dotColor);
    setBackgroundColor(values.backgroundColor);

    // Track QR code generation
    trackQRCodeGeneration(values.url, values.dotType);

    const newEntry: HistoryEntry = {
      url: values.url,
      dotType: values.dotType,
      dotColor: values.dotColor,
      backgroundColor: values.backgroundColor,
      timestamp: Date.now(),
    };

    setHistory((prevHistory) => {
      const updatedHistory = [newEntry, ...prevHistory].slice(0, MAX_HISTORY_ENTRIES);
      saveHistory(updatedHistory);
      return updatedHistory;
    });
  };

  const handleLoadHistoryEntry = (entry: HistoryEntry) => {
    setUrl(entry.url);
    setDotType(entry.dotType);
    setDotColor(entry.dotColor);
    setBackgroundColor(entry.backgroundColor);

    // Track history entry load
    trackEngagement('load_history_entry', entry.dotType);
  };

  const handleDeleteHistoryEntry = (index: number) => {
    const updatedHistory = history.filter((_, i) => i !== index);
    setHistory(updatedHistory);
    saveHistory(updatedHistory);

    // Track history entry deletion
    trackEngagement('delete_history_entry');
  };

  return (
    <Stack gap="xl">
      <Title order={1}>QR Code Generator</Title>

      <Grid
        gutter="xl"
        breakpoints={{ xs: '320px', sm: '640px', md: '768px', lg: '1024px', xl: '1200px' }}
      >
        <Grid.Col
          span={{
            sm: 12,
            md: 6,
            lg: 8,
          }}
          order={{ base: 2, md: 1 }}
        >
          <Card shadow="sm" padding="lg" radius="md" withBorder>
            <Stack>
              <QRCodeForm
                initialUrl={url}
                initialDotType={dotType}
                initialDotColor={dotColor}
                initialBackgroundColor={backgroundColor}
                onGenerate={handleGenerateQRCode}
                onDotTypeChange={setDotType}
                onDotColorChange={setDotColor}
                onBackgroundColorChange={setBackgroundColor}
              />
              <Flex gap="md">
                <Button
                  style={{ flexGrow: 1 }}
                  size="md"
                  variant="light"
                  onClick={() => {
                    window.open(url, '_blank');
                  }}
                >
                  Open URL
                </Button>
                <Button
                  style={{ flexGrow: 1 }}
                  size="md"
                  variant="light"
                  onClick={() => {
                    if (qrCodeRef.current) {
                      qrCodeRef.current.download({ name: 'qrcode', extension: 'png' });
                      trackQRCodeDownload('png');
                    }
                  }}
                >
                  Download QR Code
                </Button>
                <Button
                  style={{ flexGrow: 1 }}
                  size="md"
                  color="pink"
                  variant="light"
                  onClick={() => {
                    // set options to default
                    setUrl(DEFAULT_URL);
                    setDotType(DEFAULT_DOT_TYPE);
                    setDotColor(DEFAULT_DOT_COLOR);
                    setBackgroundColor(DEFAULT_BACKGROUND_COLOR);
                  }}
                >
                  Clear
                </Button>
              </Flex>
            </Stack>
          </Card>
        </Grid.Col>
        <Grid.Col
          span={{
            sm: 12,
            md: 6,
            lg: 4,
          }}
          order={{ base: 1, md: 2 }}
        >
          <Center h="100%">
            <QRCodeDisplay
              data={url || process.env.NEXT_PUBLIC_SITE_URL}
              dotType={dotType}
              dotColor={dotColor}
              backgroundColor={backgroundColor}
              qrCodeRef={qrCodeRef}
            />
          </Center>
        </Grid.Col>
      </Grid>
      <EzoicAdPlacement placementId={114} />

      <QRCodeHistory
        history={history}
        onLoadHistoryEntry={handleLoadHistoryEntry}
        onDeleteHistoryEntry={handleDeleteHistoryEntry}
      />
    </Stack>
  );
}
