'use client';
import { useCallback, useRef, useState } from 'react';
import { Stack, Grid, Title, Text, Button, Center } from '@mantine/core';
import QRCodeStyling, { DotType } from 'qr-code-styling';
import { PayNowForm } from './components/PayNowForm';
import { QRCodeDisplay } from './components/QRCodeDisplay';
import { useAnalytics } from './hooks/useAnalytics';
import { PayNowOptions, generatePayNowQRCode } from './utils/paynow';

const DEFAULT_DOT_TYPE: DotType = 'square';
const DEFAULT_DOT_COLOR = '#91137b';
const DEFAULT_BACKGROUND_COLOR = '#ffffff';

export default function PayNowQRCodePage() {
  const [qrData, setQrData] = useState('');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const { trackQRCodeGeneration, trackQRCodeDownload } = useAnalytics();
  const qrCodeRef = useRef<QRCodeStyling | null>(null);
  const [fileName, setFileName] = useState('');

  const handleGeneratePayNowQR = useCallback(
    (options: PayNowOptions) => {
      setErrorMessage(null); // Clear previous errors
      setFileName(`PayNow-${options.identifierType}-${options.identifier}`);
      try {
        const generatedQRData = generatePayNowQRCode(options);
        setQrData(generatedQRData);

        // Track QR code generation
        trackQRCodeGeneration(
          `PayNow-${options.identifierType}-${options.amount}`,
          DEFAULT_DOT_TYPE
        );
      } catch (error) {
        console.error('Failed to generate PayNow QR code:', error);
        setErrorMessage('Failed to generate QR code. Please check your input.');
      }
    },
    [trackQRCodeGeneration, DEFAULT_DOT_TYPE]
  );

  return (
    <Stack gap="xl">
      <Stack gap="sm">
        <Title order={1}>Singapore PayNow QR Code Generator</Title>
        {errorMessage && (
          <Text color="red" size="sm">
            {errorMessage}
          </Text>
        )}
      </Stack>

      <Grid
        gutter="xl"
        breakpoints={{ xs: '320px', sm: '640px', md: '768px', lg: '1024px', xl: '1200px' }}
      >
        <Grid.Col
          span={{
            sm: 12,
            md: 6,
            lg: 8,
          }}
          order={{ base: 2, md: 1 }}
        >
          <PayNowForm onGenerate={handleGeneratePayNowQR} />
        </Grid.Col>
        <Grid.Col
          span={{
            sm: 12,
            md: 6,
            lg: 4,
          }}
          order={{ base: 1, md: 2 }}
        >
          <Center>
            <Stack>
              <QRCodeDisplay
                data={qrData}
                dotType={DEFAULT_DOT_TYPE}
                dotColor={DEFAULT_DOT_COLOR}
                backgroundColor={DEFAULT_BACKGROUND_COLOR}
                image="/paynow-logo.png"
                qrCodeRef={qrCodeRef}
              />
              <Button
                disabled={!qrData}
                size="md"
                variant="outline"
                color="gray"
                onClick={() => {
                  qrCodeRef.current?.download({ name: fileName, extension: 'png' });
                  trackQRCodeDownload('png');
                }}
              >
                Download
              </Button>
            </Stack>
          </Center>
        </Grid.Col>
      </Grid>
    </Stack>
  );
}
