import { Typo<PERSON><PERSON>ty<PERSON><PERSON>rovider } from '@mantine/core';
import ReactMarkdown from 'react-markdown';
import Link from 'next/link';

export default function AboutQRCodePage() {
  const markdownContent = `
# What is a QR Code?

A QR code (short for Quick Response code) is a type of matrix barcode (or two-dimensional barcode)
first designed in 1994 for the automotive industry in Japan. A barcode is a machine-readable
optical label that contains information about the item to which it is attached.

## History of QR Codes

QR codes were invented by <PERSON><PERSON><PERSON> from the Japanese company Denso Wave in 1994.
Initially, they were used to track vehicles during manufacturing and to manage parts inventory.
Their ability to store more information than traditional barcodes and be read quickly
led to their adoption in various other industries.

## How Do QR Codes Work?

A QR code consists of black squares arranged in a square grid on a white background.
These patterns represent binary data that can be read by an imaging device, such as a camera,
and then processed by a processor until the data is extracted. The three large squares at the
corners of the QR code are "finder patterns" that help the scanner identify the code's orientation
and distinguish it from other elements in an image.

## What Can QR Codes Store?

QR codes have become widely popular due to their fast readability and greater storage capacity
compared to standard UPC barcodes. They can store various types of data, such as:

*   **URLs (website links):** The most common use, directing users to websites, videos, or online profiles.
*   **Text messages:** Simple plain text that can be displayed.
*   **Contact information (vCards):** Allows users to quickly add contact details to their address book.
*   **Wi-Fi network credentials:** Simplifies connecting to Wi-Fi networks by encoding SSID, password, and encryption type.
*   **Geographic coordinates:** Can open map applications to a specific location.
*   **Payment information:** Used in mobile payment systems for quick transactions.
*   **Application downloads:** Links directly to app store pages.
*   **Event details:** Add events directly to a calendar.

## How to Use a QR Code

To use a QR code, simply open your smartphone's camera or a dedicated QR code scanner app,
point it at the code, and it will automatically detect and interpret the information.
Most modern smartphones have built-in QR code scanning capabilities within their camera apps.

QR Code Donkey provides a free and easy way to generate custom QR codes for your needs!

[Back to QR Code Generator](/ "Back to QR Code Generator")
  `;

  return (
    <TypographyStylesProvider>
      <ReactMarkdown
        components={{
          a: ({ href, children }) => (
            <Link
              href={href || '/'}
              style={{ textDecoration: 'none', textAlign: 'center' }}
            >
              {children}
            </Link>
          ),
        }}
      >
        {markdownContent}
      </ReactMarkdown>
    </TypographyStylesProvider>
  );
}
