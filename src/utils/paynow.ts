/**
 * Singapore PayNow QR Code Generator Utilities
 * Based on SGQR specifications and EMVCo standards
 */

export interface PayNowOptions {
  /** UEN (for companies) or phone number (for individuals) */
  identifier: string;
  /** Type of identifier: 'uen' for company UEN, 'mobile' for phone number */
  identifierType: 'uen' | 'mobile';
  /** Transaction amount in SGD */
  amount: number;
  /** Whether the amount can be edited by the payer (0 = not editable, 1 = editable) */
  editable: 0 | 1;
  /** Expiry date in YYYYMMDD or YYYYMMDDHHmmss format */
  expiry?: string;
  /** Reference number for tracking */
  refNumber?: string;
  /** Merchant/Company name */
  merchantName?: string;
  /** Merchant city (defaults to Singapore) */
  merchantCity?: string;
}

/**
 * Pad string to left with specified character
 */
function padLeft(str: string, length: number, padChar: string = '0'): string {
  if (str.length >= length) {
    return str;
  }
  return Array(length - str.length + 1).join(padChar) + str;
}

/**
 * Calculate CRC16 checksum according to ISO/IEC 13239
 * Uses polynomial 0x1021 and initial value 0xFFFF
 */
function crc16(data: string): string {
  const crcTable = [
    0x0000, 0x1021, 0x2042, 0x3063, 0x4084, 0x50a5,
    0x60c6, 0x70e7, 0x8108, 0x9129, 0xa14a, 0xb16b,
    0xc18c, 0xd1ad, 0xe1ce, 0xf1ef, 0x1231, 0x0210,
    0x3273, 0x2252, 0x52b5, 0x4294, 0x72f7, 0x62d6,
    0x9339, 0x8318, 0xb37b, 0xa35a, 0xd3bd, 0xc39c,
    0xf3ff, 0xe3de, 0x2462, 0x3443, 0x0420, 0x1401,
    0x64e6, 0x74c7, 0x44a4, 0x5485, 0xa56a, 0xb54b,
    0x8528, 0x9509, 0xe5ee, 0xf5cf, 0xc5ac, 0xd58d,
    0x3653, 0x2672, 0x1611, 0x0630, 0x76d7, 0x66f6,
    0x5695, 0x46b4, 0xb75b, 0xa77a, 0x9719, 0x8738,
    0xf7df, 0xe7fe, 0xd79d, 0xc7bc, 0x48c4, 0x58e5,
    0x6886, 0x78a7, 0x0840, 0x1861, 0x2802, 0x3823,
    0xc9cc, 0xd9ed, 0xe98e, 0xf9af, 0x8948, 0x9969,
    0xa90a, 0xb92b, 0x5af5, 0x4ad4, 0x7ab7, 0x6a96,
    0x1a71, 0x0a50, 0x3a33, 0x2a12, 0xdbfd, 0xcbdc,
    0xfbbf, 0xeb9e, 0x9b79, 0x8b58, 0xbb3b, 0xab1a,
    0x6ca6, 0x7c87, 0x4ce4, 0x5cc5, 0x2c22, 0x3c03,
    0x0c60, 0x1c41, 0xedae, 0xfd8f, 0xcdec, 0xddcd,
    0xad2a, 0xbd0b, 0x8d68, 0x9d49, 0x7e97, 0x6eb6,
    0x5ed5, 0x4ef4, 0x3e13, 0x2e32, 0x1e51, 0x0e70,
    0xff9f, 0xefbe, 0xdfdd, 0xcffc, 0xbf1b, 0xaf3a,
    0x9f59, 0x8f78, 0x9188, 0x81a9, 0xb1ca, 0xa1eb,
    0xd10c, 0xc12d, 0xf14e, 0xe16f, 0x1080, 0x00a1,
    0x30c2, 0x20e3, 0x5004, 0x4025, 0x7046, 0x6067,
    0x83b9, 0x9398, 0xa3fb, 0xb3da, 0xc33d, 0xd31c,
    0xe37f, 0xf35e, 0x02b1, 0x1290, 0x22f3, 0x32d2,
    0x4235, 0x5214, 0x6277, 0x7256, 0xb5ea, 0xa5cb,
    0x95a8, 0x8589, 0xf56e, 0xe54f, 0xd52c, 0xc50d,
    0x34e2, 0x24c3, 0x14a0, 0x0481, 0x7466, 0x6447,
    0x5424, 0x4405, 0xa7db, 0xb7fa, 0x8799, 0x97b8,
    0xe75f, 0xf77e, 0xc71d, 0xd73c, 0x26d3, 0x36f2,
    0x0691, 0x16b0, 0x6657, 0x7676, 0x4615, 0x5634,
    0xd94c, 0xc96d, 0xf90e, 0xe92f, 0x99c8, 0x89e9,
    0xb98a, 0xa9ab, 0x5844, 0x4865, 0x7806, 0x6827,
    0x18c0, 0x08e1, 0x3882, 0x28a3, 0xcb7d, 0xdb5c,
    0xeb3f, 0xfb1e, 0x8bf9, 0x9bd8, 0xabbb, 0xbb9a,
    0x4a75, 0x5a54, 0x6a37, 0x7a16, 0x0af1, 0x1ad0,
    0x2ab3, 0x3a92, 0xfd2e, 0xed0f, 0xdd6c, 0xcd4d,
    0xbdaa, 0xad8b, 0x9de8, 0x8dc9, 0x7c26, 0x6c07,
    0x5c64, 0x4c45, 0x3ca2, 0x2c83, 0x1ce0, 0x0cc1,
    0xef1f, 0xff3e, 0xcf5d, 0xdf7c, 0xaf9b, 0xbfba,
    0x8fd9, 0x9ff8, 0x6e17, 0x7e36, 0x4e55, 0x5e74,
    0x2e93, 0x3eb2, 0x0ed1, 0x1ef0
  ];

  let crc = 0xFFFF;
  
  for (let i = 0; i < data.length; i++) {
    const c = data.charCodeAt(i);
    if (c > 255) {
      throw new RangeError('Character out of range');
    }
    const j = (c ^ (crc >> 8)) & 0xFF;
    crc = crcTable[j] ^ (crc << 8);
  }

  return ((crc ^ 0) & 0xFFFF).toString(16).toUpperCase().padStart(4, '0');
}

/**
 * Format identifier based on type
 */
function formatIdentifier(identifier: string, type: 'uen' | 'mobile'): string {
  if (type === 'mobile') {
    // Ensure mobile number starts with +65 for Singapore
    if (!identifier.startsWith('+')) {
      if (identifier.startsWith('65')) {
        return '+' + identifier;
      } else if (identifier.startsWith('8') || identifier.startsWith('9')) {
        return '+65' + identifier;
      } else {
        throw new Error('Invalid mobile number format');
      }
    }
    return identifier;
  } else {
    // UEN format validation (basic)
    if (identifier.length < 9 || identifier.length > 13) {
      throw new Error('Invalid UEN format');
    }
    return identifier.toUpperCase();
  }
}

/**
 * Validate PayNow options
 */
function validateOptions(options: PayNowOptions): void {
  if (!options.identifier) {
    throw new Error('Identifier is required');
  }
  
  if (!options.editable && options.amount <= 0) {
    throw new Error('Amount must be greater than 0');
  }
  
  if (options.amount > 999999.99) {
    throw new Error('Amount cannot exceed 999,999.99');
  }
  
  if (options.expiry) {
    const expiryRegex = /^\d{8}(\d{6})?$/; // YYYYMMDD or YYYYMMDDHHmmss
    if (!expiryRegex.test(options.expiry)) {
      throw new Error('Expiry date must be in YYYYMMDD or YYYYMMDDHHmmss format');
    }
  }
  
  if (options.refNumber && options.refNumber.length > 25) {
    throw new Error('Reference number cannot exceed 25 characters');
  }
}

/**
 * Generate PayNow QR code data string
 */
export function generatePayNowQRCode(options: PayNowOptions): string {
  validateOptions(options);
  
  const formattedIdentifier = formatIdentifier(options.identifier, options.identifierType);
  const proxyType = options.identifierType === 'mobile' ? '0' : '2';
  
  // Build data objects array
  const dataObjects = [
    { id: '00', value: '01' }, // Payload Format Indicator
    { id: '01', value: '12' }, // Point of Initiation Method (12 = dynamic)
    {
      id: '26', // Merchant Account Information
      value: [
        { id: '00', value: 'SG.PAYNOW' },
        { id: '01', value: proxyType },
        { id: '02', value: formattedIdentifier },
        { id: '03', value: options.editable.toString() },
        ...(options.expiry ? [{ id: '04', value: options.expiry }] : [])
      ]
    },
    { id: '52', value: '0000' }, // Merchant Category Code
    { id: '53', value: '702' }, // Transaction Currency (SGD)
    { id: '54', value: options.amount.toFixed(2) }, // Transaction Amount
    { id: '58', value: 'SG' }, // Country Code
    { id: '59', value: options.merchantName || 'NA' }, // Merchant Name
    { id: '60', value: options.merchantCity || 'Singapore' }, // Merchant City
    ...(options.refNumber ? [{
      id: '62', // Additional Data Field Template
      value: [{ id: '01', value: options.refNumber }]
    }] : [])
  ];

  // Build the QR code string
  let qrString = '';
  
  for (const obj of dataObjects) {
    if (Array.isArray(obj.value)) {
      // Handle nested objects
      let nestedValue = '';
      for (const nestedObj of obj.value) {
        nestedValue += nestedObj.id + padLeft(nestedObj.value.length.toString(), 2) + nestedObj.value;
      }
      qrString += obj.id + padLeft(nestedValue.length.toString(), 2) + nestedValue;
    } else {
      qrString += obj.id + padLeft(obj.value.length.toString(), 2) + obj.value;
    }
  }

  // Add CRC checksum
  const crcInput = qrString + '6304';
  const checksum = crc16(crcInput);
  qrString += '6304' + checksum;

  return qrString;
}

/**
 * Validate Singapore UEN format
 */
export function isValidUEN(uen: string): boolean {
  // UEN validation based on Singapore UEN formats:
  // 1. Sole proprietorships/partnerships (ACRA): 8 digits followed by 1 letter (e.g., 53212345A)
  // 2. Local companies (ACRA): 10 digits followed by 1 letter (e.g., 202312345A)
  // 3. Limited Liability Partnerships (LLPs) (ACRA): T + 2 digits + 2 letters + 4 digits + 1 letter (e.g., T23LL1234A)
  // 4. Registered Societies (ROS): S + 2 digits + 2 letters + 4 digits + 1 letter (e.g., S99SS1234A)
  // 5. Trade Unions: T + 2 digits + "UE" + 4 digits + 1 letter (e.g., T12UE1234A)
  const uenRegex = /^(?:[0-9]{8}[A-Z]|[0-9]{10}[A-Z]|T[0-9]{2}[A-Z]{2}[0-9]{4}[A-Z]|S[0-9]{2}[A-Z]{2}[0-9]{4}[A-Z]|T[0-9]{2}UE[0-9]{4}[A-Z])$/;
  return uenRegex.test(uen.toUpperCase());
}

/**
 * Validate Singapore mobile number format
 */
export function isValidSingaporeMobile(mobile: string): boolean {
  // Remove spaces and dashes
  const cleaned = mobile.replace(/[\s-]/g, '');
  
  // Check various formats
  const patterns = [
    /^\+658[0-9]{7}$/, // +658xxxxxxx
    /^\+659[0-9]{7}$/, // +659xxxxxxx
    /^658[0-9]{7}$/, // 658xxxxxxx
    /^659[0-9]{7}$/, // 659xxxxxxx
    /^8[0-9]{7}$/, // 8xxxxxxx
    /^9[0-9]{7}$/, // 9xxxxxxx
  ];
  
  return patterns.some(pattern => pattern.test(cleaned));
}
