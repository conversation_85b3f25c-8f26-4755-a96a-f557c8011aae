'use client';

import { useCallback } from 'react';

interface AnalyticsEvent {
  action: string;
  category: string;
  label?: string;
  value?: number;
}

interface ConversionEvent {
  currency?: string;
  value?: number;
  transaction_id?: string;
  items?: Array<{
    item_id: string;
    item_name: string;
    category?: string;
    quantity?: number;
    price?: number;
  }>;
}

export function useAnalytics() {
  // Track custom events
  const trackEvent = useCallback(({ action, category, label, value }: AnalyticsEvent) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', action, {
        event_category: category,
        event_label: label,
        value: value,
      });
    }
  }, []);

  // Track page views
  const trackPageView = useCallback((url: string, title?: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID!, {
        page_title: title,
        page_location: url,
      });
    }
  }, []);

  // Track QR code generation (specific to your app)
  const trackQRCodeGeneration = useCallback((url: string, dotType: string) => {
    trackEvent({
      action: 'generate_qr_code',
      category: 'QR Code',
      label: `${dotType}_${url.length > 50 ? 'long_url' : 'short_url'}`,
    });
  }, [trackEvent]);

  // Track QR code download
  const trackQRCodeDownload = useCallback((format: string) => {
    trackEvent({
      action: 'download_qr_code',
      category: 'QR Code',
      label: format,
    });
  }, [trackEvent]);

  // Track conversions (for Google Ads)
  const trackConversion = useCallback((conversionLabel: string, data?: ConversionEvent) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'conversion', {
        send_to: `${process.env.NEXT_PUBLIC_GOOGLE_ADS_CLIENT_ID}/${conversionLabel}`,
        ...data,
      });
    }
  }, []);

  // Track user engagement
  const trackEngagement = useCallback((action: string, details?: string) => {
    trackEvent({
      action,
      category: 'User Engagement',
      label: details,
    });
  }, [trackEvent]);

  return {
    trackEvent,
    trackPageView,
    trackQRCodeGeneration,
    trackQRCodeDownload,
    trackConversion,
    trackEngagement,
  };
}
