{"name": "qr-code-donkey", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next export"}, "dependencies": {"@mantine/core": "^7.16.0", "@mantine/form": "^7.16.0", "@mantine/hooks": "^7.16.0", "@tabler/icons-react": "^3.34.0", "next": "^15.4.2", "qr-code-styling": "^1.9.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/node": "^24.0.15", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.1", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2"}}